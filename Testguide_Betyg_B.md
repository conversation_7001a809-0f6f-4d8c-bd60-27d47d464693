# Testguide för Betyg B - Bokningssystem

## Översikt
Denna guide hj<PERSON><PERSON>per dig att systematiskt demonstrera att ditt bokningssystem uppfyller alla krav för betyg B (E + C + 2 av 3 A-krav).

## F<PERSON><PERSON><PERSON><PERSON>er innan demonstration

### 1. Starta systemet
```bash
# Från projektets rotmapp
npm start
```

### 2. Verifiera HTTPS-anslutning
- Öppna webbläsaren och gå till: `https://localhost:8989`
- Acceptera säkerhetscertifikatet (self-signed)
- Kontrollera att URL:en visar "https://" och låsikonen

### 3. Öppna utvecklarverktyg
- Tryck F12 för att öppna Developer Tools
- Gå till Network-fliken för att se HTTP-requests
- Gå till Console-fliken för att se eventuella fel

## E-krav (Grundläggande funktionalitet)

### ✅ E1: SPA med Vue.js och Vue Router
**Test:**
1. Navigera mellan olika sidor (hem, bok<PERSON><PERSON><PERSON>or, mina bokning<PERSON>)
2. Visa att sidan inte laddas om (ingen vit blink)
3. Kontrollera i Network-fliken att endast AJAX-anrop görs

**Förväntad resultat:** Smidig navigation utan sidladdningar

### ✅ E2: Node.js/Express backend
**Test:**
1. Öppna Network-fliken i Developer Tools
2. Gör en bokning eller logga in
3. Visa API-anrop till `/api/*` endpoints

**Förväntad resultat:** API-anrop till Express-servern syns

### ✅ E3: SQLite databas med persistens
**Test:**
1. Gör en bokning
2. Stäng webbläsaren helt
3. Starta om servern: `Ctrl+C` och sedan `npm start`
4. Öppna webbläsaren igen och logga in
5. Kontrollera att bokningen finns kvar

**Förväntad resultat:** Data finns kvar efter omstart

### ✅ E4: Autentisering och sessionshantering
**Test:**
1. Försök komma åt `/booking-lists` utan att logga in
2. Logga in som student
3. Kontrollera att du kan komma åt skyddade sidor
4. Logga ut och försök komma åt skyddade sidor igen

**Förväntad resultat:** Omdirigering till login när ej autentiserad

### ✅ E5: Admin-funktioner
**Test:**
1. Gå till `/admin/login`
2. Logga in som admin (användarnamn: admin, lösenord: admin123)
3. Skapa en ny bokningslista
4. Lägg till tidsluckor
5. Modifiera en befintlig bokningslista
6. Ta bort en tidslucka

**Förväntad resultat:** Alla admin-funktioner fungerar

### ✅ E6: Student-funktioner
**Test:**
1. Logga in som student
2. Visa tillgängliga bokningslistor
3. Boka en tid
4. Visa "Mina bokningar"
5. Avboka en tid

**Förväntad resultat:** Alla student-funktioner fungerar

### ✅ E7: Socket.io realtidsuppdateringar
**Test:**
1. Öppna två webbläsarfönster
2. Logga in som admin i ena, student i andra
3. Skapa en ny tidslucka som admin
4. Kontrollera att den syns direkt hos studenten

**Förväntad resultat:** Ändringar syns omedelbart i båda fönstren

## C-krav (Avancerad funktionalitet)

### ✅ C1: Gruppbokningar
**Test:**
1. Logga in som student
2. Skapa en grupp eller gå med i befintlig grupp
3. Boka en tid som grupp
4. Logga in som annan gruppmedlem
5. Kontrollera att gruppmedlemmen kan avboka tiden

**Förväntad resultat:** Gruppmedlemmar kan hantera varandras bokningar

### ✅ C2: Tidsbegränsningar
**Test:**
1. Som admin, skapa en bokningslista med:
   - Synlighetsperiod (t.ex. från imorgon)
   - Bokningsperiod (t.ex. från nästa vecka)
   - Avbokningsdeadline
2. Som student, försök boka utanför tillåten period

**Förväntad resultat:** Systemet förhindrar bokningar utanför tillåtna tider

### ✅ C3: Assistenthantering
**Test:**
1. Som admin, gå till assistenthantering
2. Skapa en ny assistent
3. Tilldela assistenten till en bokningslista
4. Som student, kontrollera att assistentinformation visas

**Förväntad resultat:** Assistenter kan skapas och tilldelas

### ✅ C4: Soft deletion av avbokningar
**Test:**
1. Som student, boka en tid
2. Avboka tiden
3. Som admin, kontrollera bokningshistorik
4. Verifiera att avbokad tid finns kvar som "avbokad"

**Förväntad resultat:** Avbokade tider markeras som avbokade, raderas inte

## A-krav (2 av 3 behövs för betyg B)

### ✅ A1: XSS-skydd (Helmet + express-validator)

#### Test 1: Helmet headers
**Test:**
1. Öppna Developer Tools → Network
2. Ladda om sidan
3. Klicka på huvuddokumentet i Network-listan
4. Kontrollera Response Headers

**Förväntade headers:**
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Content-Security-Policy: ...`

#### Test 2: Input validation
**Test:**
1. Försök logga in med tomt användarnamn
2. Försök logga in med för långt användarnamn (>50 tecken)
3. Kontrollera felmeddelanden

**Förväntad resultat:** Tydliga valideringsfel visas

#### Test 3: XSS-skydd
**Test:**
1. Försök logga in med användarnamn: `<script>alert('XSS')</script>`
2. Kontrollera att skriptet inte körs
3. Kontrollera i Network att farliga tecken escapas

**Förväntad resultat:** Skript körs inte, farliga tecken konverteras

### ✅ A2: HTTPS-implementation

#### Test 1: HTTPS-anslutning
**Test:**
1. Gå till `https://localhost:8989`
2. Kontrollera låsikonen i adressfältet
3. Klicka på låsikonen och visa certifikatinformation

**Förväntad resultat:** Giltigt SSL-certifikat visas

#### Test 2: HTTP till HTTPS-omdirigering
**Test:**
1. Gå till `http://localhost:8080`
2. Kontrollera att du omdirigeras till HTTPS

**Förväntad resultat:** Automatisk omdirigering till HTTPS

#### Test 3: Säkra cookies
**Test:**
1. Logga in via HTTPS
2. Öppna Developer Tools → Application → Cookies
3. Kontrollera session-cookien

**Förväntade attribut:**
- `Secure: true`
- `HttpOnly: true`
- `SameSite: Strict`

### ❌ A3: Levande resurser (hoppar över detta)

## Sammanfattning för redovisning

### Krav som uppfylls:
- ✅ Alla E-krav (7/7)
- ✅ Alla C-krav (4/4)  
- ✅ 2 av 3 A-krav (XSS-skydd + HTTPS)

### Resultat: BETYG B uppfyllt! 🎉

## Tekniska detaljer att nämna

### Säkerhetsimplementationer:
1. **Helmet**: Säkra HTTP-headers
2. **express-validator**: Serversidesvalidering
3. **HTTPS**: Krypterad kommunikation
4. **Säkra cookies**: HttpOnly, Secure, SameSite
5. **Input escaping**: XSS-skydd
6. **CORS**: Kontrollerad resursdelning

### Arkitektur:
1. **Frontend**: Vue.js SPA med Vue Router
2. **Backend**: Node.js/Express med säker konfiguration
3. **Databas**: SQLite med transaktioner
4. **Realtid**: Socket.io över HTTPS
5. **Sessioner**: Express-session med säkra inställningar

## Felsökning

### Om HTTPS inte fungerar:
```bash
cd server
./generate-cert.sh
npm start
```

### Om valideringsfel:
- Kontrollera att express-validator är installerat
- Kontrollera att middleware används i rätt ordning

### Om Socket.io inte fungerar:
- Kontrollera att CORS är korrekt konfigurerat
- Kontrollera att certifikat är giltiga
